{"data": [{"hovertemplate": "Site=%{x}<br>Invoiced Consumption (kWh)=%{marker.color}<extra></extra>", "legendgroup": "", "marker": {"color": "#4caf50", "coloraxis": "coloraxis", "pattern": {"shape": ""}}, "name": "", "orientation": "v", "showlegend": false, "textposition": "auto", "x": ["COUR_PULV", "NANTE_HD_IMM_ARENA_92", "MARN_HARAS_DE_JARDY", "BOUL_JARDIN_KAHN", "PUTEA_COLL_MARECHAL_LECLERC", "CHAT_PARC_VALLEE_AUX_LOUPS", "SCEA_DOMAINE_DE_SCEAUX", "CHAT_NOHA_PISCINE_PROVISOIRE", "NANTE_HD_IMM_LE_SALVADOR_61_ALLENDE", "RUEIL_COLL_LES_MARTINETS", "MEUD_COLL_BEJART", "FONT_COLL_LES_ORMEAUX", "NANTE_HD_IMM_2_SOUFFLOT", "RUEIL_COLL_DUNANT", "CLIC_COLL_JAURES", "RUEIL_COLL_VERNE", "RUEIL_COLL_PAGNOL", "ANTO_COLL_FRANK", "VILGA_PARC_CHANTERAINES", "LEVA_COLL_BLERIOT", "COLO_STADE_DU_MANOIR", "PUTEA_HD_IMM_SOWORK_14_HOCHE", "NANTE_HD_EXTENSION_28_ZOLA", "CLIC_COLL_MACE", "ISSY_COLL_MANDEL", "CLIC_COLL_VAN_GOGH", "RUEIL_COLL_LES_BONS_RAISINS", "PLES_PAV_21_LECLERC", "CHATI_COLL_ELUARD", "GENN_COLL_MOQUET", "BOUL_COLL_RENOIR", "ISSY_COLL_DE_LA_PAIX_2", "MONT_COLL_DOISNEAU", "NANTE_GARAGE_DEP_32_FRACHON", "BAGN_COLL_ROLLAND", "ST_CL_COLL_VERHAEREN", "ASNI_COLL_MALRAUX", "ASNI_SLT", "ANTO_COLL_DESCARTES", "LEVA_COLL_DANTON", "GENN_COLL_VAILLANT", "NANTE_POLSOC_LE_QUARTZ_4_FRACHON", "SEVR_PARC_NAUTIQUE_ILE_MONSIEUR", "NANTE_ARCHIVES_DEP_137_CURIE", "CHAT_POLSOC_9_VERNE", "BOUR_COLL_GALOIS", "NANTE_COLL_ELUARD", "CLAM_COLL_ALAIN_FOURNIER", "NANTE_COLL_LES_CHENEVREUX", "GARC_COLL_BERGSON", "SURE_COLL_SELLIER", "ASNI_POLSOC_1_DELORME", "ANTO_GYMN_LA_FONTAINE", "BCOL_COLL_MERMOZ", "BOUL_SLT", "BOUL_COLL_AURIOL", "COLO_COLL_DURAS", "NANTE_PARC_MALRAUX", "CLAM_COLL_LES_PETITS_PONTS", "CHAV_COLL_MOULIN", "COUR_COLL_SEURAT", "SEVR_COLL_DE_SEVRES", "CHATI_SLT", "BOUL_COLL_LANDOWSKI", "ASNI_COLL_TRUFFAUT", "CLAM_COLL_MAISON_BLANCHE", "PLES_COLL_ROLLAND", "COUR_COLL_LES_RENARDIERES", "LEVA_COLL_JAURES", "CHATI_ASE_CENTRE_MATERNEL", "RUEIL_POLSOC_16B_REPUBLIQUE", "SEVR_SLT", "BOUR_POLSOC_143_LECLERC", "NANTE_COLL_PERRIN", "VILGA_COLL_POMPIDOU", "PLES_ASE_POUPONNIERE", "ISSY_PARC_ILE_ST_GERMAIN", "COLO_COLL_CLEMENT", "NEUI_COLL_GAUTIER", "ISSY_COLL_MATISSE", "ANTO_COLL_FURET", "RUEIL_COLL_LA_MALMAISON", "CLIC_NOHA_PISCINE_PROVISOIRE", "COLO_COLL_LAKANAL", "BCOL_COLL_CAMUS", "NANTE_SLT", "COUR_COLL_POMPIDOU", "GENN_COLL_PASTEUR", "NANTE_CTD_141_COURBEVOIE", "SURE_COLL_MACE", "GARE_COLL_LES_VALLEES", "GARE_COLL_LES_CHAMPS_PHILIPPE", "NANTE_PARC_CHEMIN_ILE", "COUR_COLL_LES_BRUYERES", "ISSY_COLL_HUGO", "VAUC_COLL_DU_MANOIR", "ANTO_LA_GRENOUILLERE", "CHAT_SLT", "ANTO_COLL_LA_FONTAINE", "ASNI_COLL_RENOIR", "PUTEA_SLT", "NANTE_COLL_REPUBLIQUE", "CHATI_COLL_SAND", "SEVR_JAD_6_GRANDE_RUE", "VILGA_COLL_MANET", "PLES_ASE_CITE_ENFANCE", "COLO_COLL_MOULIN_JOLY", "MEUD_COLL_BEL_AIR", "COUR_COLL_DE_VIGNY", "MONT_COLL_GENEVOIX", "ASNI_IMM_2_DUPONT", "COLO_COLL_GAY_LUSSAC", "COLO_COLL_PAPAREMBORDE", "ISSY_IMM_13_TIMBAUD", "GEN_SLT", "ST_CL_SLT", "VANV_COLL_SAINT_EXUPERY", "NANTE_COLL_DOUCET", "ASNI_POLSOC_RINER", "NANTE_COLL_GALOIS", "MALA_COLL_BERT", "VILAV_COLL_LA_FONTAINE_DU_ROY", "NANTE_COLL_HUGO", "CLAM_SLT", "COUR_SLT", "CHAT_COLL_MASARYK", "GENN_STADE_CHAZOTTE", "ST_CL_COLL_GOUNOD", "CHAT_COLL_BROSSOLETTE", "NEUI_COLL_MAUROIS", "PUTEA_COLL_LES_BOUVETS", "CLIC_POLSOC_20_DOCTEUR_ROUX", "MONT_COLL_DU_HAUT_MESNIL", "BOUL_COLL_BARTHOLDI", "SURE_COLL_ZOLA", "PLES_COLL_LEDOUX", "MARN_SLT", "NEUIL_SLT", "BOUL_VOIRIE_4_SEPTEMBRE", "COLO_PARC_LAGRAVERE", "BAGN_POLSOC_13_PERI", "ASNI_COLL_VOLTAIRE", "BAGN_COLL_BARBUSSE", "VILGA_POLSOC_54_LECLERC", "ISSY_SLT", "CHAT_COLL_DE_VINCI", "COLO_IMM_32_SEINE_ARCHIVES", "BOUL_IMM_SOUS_PREFECTURE", "ST_CL_STADE_PRE_SAINT_JEAN", "COLO_POLSOC_LE_VEDRINES", "LEVA_SLT", "MONTL_ASE_80_VILLEBRET", "BAGN_COLL_JOLIOT_CURIE", "ASNI_IMM_10_DUPONT", "CHATI_POLSOC_39_LOUVEAU", "NANTE_MDPH_2_RIGAULT", "SURE_EDAS_18_BOURETS", "GENN_EDAS_42_CALMEL", "VANV_POLSOC_12_BESSEYRE", "COLO_CPEF_267_GROS_GRES", "COUR_PMI_176_ST_DENIS", "MALA_COLL_WALLON", "GENN_VOIRIE_64_BAS", "PLES_PARC_ETANG_COLBERT", "CHAT_VOIRIE_CHATENAY", "COLO_SLT", "LEVA_POLSOC_1B_COLLANGE", "VANV_VOIRIE_6_PAIX", "NEUI_POLSOC_11_PILOT", "ANTO_PMI_13_MEDITERRANEE", "BOUL_EDAS_76_REPUBLIQUE", "NANTE_HD_IMM_105_ARAGO", "SEVR_EDAS_64_BINELLES", "NOUZ_DOMAINE_ORFRASIERE", "MEUD_POLSOC_3_GALONS", "ANTO_COULEE_VERTE", "PLES_SLT", "BAGN_PMI_74_NAUDIN", "COUR_PMI_56_GUYNEMER", "BOUL_IMM_41_THIERS", "COLO_EI_10_BARBUSSE", "COLO_IMM_32_SEINE_DLMG", "PUTEA_PMI_3_GERHARD_PROLONGEE", "VAUC_SLT", "BARB_ASE_MAISON_FAMILIALE", "ASNI_EDAS_12T_PARISIENS", "ASNI_IMM_8_DUPONT", "ASNI_OA_PONT_D'ASNIERES", "COUR_EDAS_8_BRIAND", "MONT_PMI_43_GINOUX", "PLES_PARC_SELLIER", "GARE_POLSOC_LE_COLOMBUS_1_EUROPE", "CLIC_POLSOC_32_PALLOY", "COUR_PAV_2_CLOS_LUCE", "VILGA_SLT", "CLAM_PMI_3_EGLISE", "SURE_SLT", "BOUL_PMI_8B_ROCHEFOUCAULD", "ASNI_PMI_18B_GRESILLONS", "CHAT_IMM_12_SIMIAND", "ISSY_POLSOC_19_RESISTANCE", "GERV_ASE_32_NATIONALE", "GARC_PMI_23_QUATRE_VENTS", "SURE_IMM_5_NIEUPORT", "CHAV_POLSOC_22_HENRI_IV", "CLAM_PAV_95_RENAUDIN", "BOUL_SAINT_DENIS_(41_RUE_DE)", "MALA_POLSOC_4_VARIOT", "RUEIL_PARC_MONT_VALERIEN", "VILGA_IMM_18_DE_GAULLE", "PLES_PMI_26_DE_GAULLE", "BAGN_POLSOC_2_BARBUSSE", "RAMB_IMM_38_PATENOTRE", "COLO_IMM_42_STALINGRAD", "ANTO_COLL_ADAM", "BOUL_SEINE_MUSICALE", "NANTE_PMI_192_REPUBLIQUE", "MONT_PMI_112_DORMOY", "MALA_SLT", "GARE_IMM_2_REPUBLIQUE", "GARE_SLT", "NEUI_IMM_FOLIE_ST_JAMES_34_MADRID", "NANTE_PAV_16_MIDI", "ANTO_SLT", "SCEA_SLT", "CLAM_EDAS_10_EGLISE", "PUTEA_EDAS_34_BLANCHE", "RUEIL_SLT", "NANTE_IMM_2_GOULVENTS", "FONT_EDAS_24_LEDRU_ROLLIN", "ASNI_CMP_PARIENTE_12_DE_GAULLE", "ST_CL_PMI_1_DOCTEUR_DESFOSSEZ", "ANTO_POLSOC_2_BONE", "NANTE_PMI_9_DECOUR", "BAGN_PAV_2_PAIX", "GENN_IMM_1_ANTOINETTE", "COLO_IMM_2_ACHERES", "CLAM_PAV_15_BRETAGNE", "BOUL_OA_PONT_DE_BILLANCOURT", "ASNI_IMM_206_VOLTAIRE", "MALA_PMI_66_AVAULEE", "BCOL_POLSOC_65_LECLERC", "GARC_PARC_CASIMIR_DAVAINE", "CHATI_PAV_1_ROSTAND", "PLES_BOIS_SOLITUDE", "PLES_ASE_FOYER_COLBERT", "RUEIL_IMM_27_SOLFERINO", "SCEA_IMM_8_GUESDE", "ST_CL_CASERNE_SULLY", "ST_CL_UNSS_BESSON"], "xaxis": "x", "y": {"dtype": "f8", "bdata": "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"}, "yaxis": "y", "type": "bar"}], "layout": {"barmode": "relative", "coloraxis": {"colorbar": {"title": {"text": "Invoiced Consumption (kWh)"}}, "colorscale": [[0.0, "#440154"], [0.1111111111111111, "#482878"], [0.2222222222222222, "#3e4989"], [0.3333333333333333, "#31688e"], [0.4444444444444444, "#26828e"], [0.5555555555555556, "#1f9e89"], [0.6666666666666666, "#35b779"], [0.7777777777777778, "#6ece58"], [0.8888888888888888, "#b5de2b"], [1.0, "#fde725"]]}, "legend": {"tracegroupgap": 0}, "template": {"data": {"bar": [{"hovertemplate": "<b>%{x}</b><br>%{y}<extra></extra>", "marker": {"color": "#4caf50", "line": {"color": "rgb(237,237,237)", "width": 0.5}, "opacity": 0.9, "pattern": {"shape": "", "fillmode": "overlay", "size": 10, "solidity": 0.2}}, "textfont": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "textposition": "auto", "type": "bar", "error_x": {"color": "rgb(51,51,51)"}, "error_y": {"color": "rgb(51,51,51)"}}], "box": [{"boxmean": "sd", "fillcolor": "#c8e6c9", "line": {"color": "#388e3c", "width": 2}, "marker": {"color": "#4caf50", "line": {"color": "#388e3c", "width": 1}, "opacity": 0.8, "size": 8}, "notched": true, "whiskerwidth": 0.5, "type": "box"}], "candlestick": [{"decreasing": {"fillcolor": "#f44336", "line": {"color": "#f44336", "width": 1}}, "increasing": {"fillcolor": "#4caf50", "line": {"color": "#4caf50", "width": 1}}, "line": {"width": 1}, "whiskerwidth": 0, "type": "candlestick"}], "contour": [{"colorbar": {"bgcolor": "#ffffff", "bordercolor": "#d0d0d0", "borderwidth": 1, "len": 0.7, "thickness": 15, "outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "contours": {"labelfont": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "labelformat": ".2f", "showlabels": true}, "line": {"smoothing": 0.85, "width": 1}, "type": "contour"}], "funnel": [{"connector": {"line": {"color": "#e0e0e0", "dash": "dot", "width": 2}}, "marker": {"colorscale": [[0.0, "#e8f5e9"], [0.1, "#c8e6c9"], [0.25, "#a5d6a7"], [0.4, "#81c784"], [0.55, "#66bb6a"], [0.7, "#4caf50"], [0.85, "#388e3c"], [1.0, "#1b5e20"]], "line": {"color": "#ffffff", "width": 2}}, "opacity": 0.9, "textfont": {"color": "#ffffff", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "textinfo": "value+percent initial", "textposition": "inside", "type": "funnel"}], "heatmap": [{"colorbar": {"bgcolor": "#ffffff", "bordercolor": "#d0d0d0", "borderwidth": 1, "len": 0.7, "thickness": 15, "tickfont": {"color": "#616161", "size": 11}, "title": {"font": {"color": "#212121", "size": 11}, "side": "right"}, "outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "hovertemplate": "X: %{x}<br>Y: %{y}<br>Value: %{z}<extra></extra>", "type": "heatmap"}], "histogram2d": [{"colorbar": {"bgcolor": "#ffffff", "bordercolor": "#d0d0d0", "borderwidth": 1, "len": 0.7, "thickness": 15, "outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "hovertemplate": "X: %{x}<br>Y: %{y}<br>Count: %{z}<extra></extra>", "type": "histogram2d"}], "histogram": [{"histnorm": "", "hovertemplate": "Range: %{x}<br>Count: %{y}<extra></extra>", "marker": {"color": "#66bb6a", "line": {"color": "#388e3c", "width": 1}, "opacity": 0.85, "pattern": {"shape": "", "fillmode": "overlay", "size": 10, "solidity": 0.2}}, "nbinsx": 20, "type": "histogram"}], "indicator": [{"delta": {"font": {"size": 16}, "reference": 50}, "gauge": {"axis": {"range": [null, null], "tickcolor": "#616161", "tickfont": {"color": "#616161", "size": 11}, "tickwidth": 1}, "bar": {"color": "#4caf50", "thickness": 0.8}, "bgcolor": "#e8f5e9", "bordercolor": "#81c784", "borderwidth": 2, "steps": [{"color": "#c8e6c9", "range": [0, 50]}, {"color": "#a5d6a7", "range": [50, 80]}, {"color": "#81c784", "range": [80, 100]}], "threshold": {"line": {"color": "#f44336", "width": 4}, "thickness": 0.75, "value": 90}}, "mode": "gauge+number+delta", "number": {"font": {"color": "#388e3c", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 30.0}, "suffix": "", "valueformat": ".1f"}, "title": {"font": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 16}}, "type": "indicator"}], "parcoords": [{"labelfont": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "line": {"cmax": 1, "cmin": 0, "color": "#4caf50", "colorscale": [[0.0, "#e8f5e9"], [0.1, "#c8e6c9"], [0.25, "#a5d6a7"], [0.4, "#81c784"], [0.55, "#66bb6a"], [0.7, "#4caf50"], [0.85, "#388e3c"], [1.0, "#1b5e20"]], "showscale": true, "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "rangefont": {"color": "#616161", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "tickfont": {"color": "#616161", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "type": "parcoords"}], "pie": [{"hole": 0, "hovertemplate": "<b>%{label}</b><br>%{value}<br>%{percent}<extra></extra>", "marker": {"colors": ["#4caf50", "#2196f3", "#ff9800", "#388e3c", "#9c27b0", "#81c784", "#009688", "#2e7d32", "#f44336", "#a5d6a7"], "line": {"color": "#ffffff", "width": 2}}, "pull": [0.05, 0, 0, 0, 0, 0, 0, 0, 0, 0], "textfont": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "textinfo": "label+percent", "textposition": "auto", "type": "pie", "automargin": true}], "sankey": [{"link": {"arrowlen": 15, "color": "rgba(76, 175, 80, 0.4)", "hovertemplate": "%{source.label} → %{target.label}<br>%{value}<extra></extra>"}, "node": {"color": "#66bb6a", "hoverlabel": {"font": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}}, "line": {"color": "#388e3c", "width": 1}, "pad": 20, "thickness": 25}, "type": "sankey"}], "scatter3d": [{"line": {"color": "#4caf50", "width": 4, "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "marker": {"color": "#4caf50", "colorscale": [[0.0, "#e8f5e9"], [0.1, "#c8e6c9"], [0.25, "#a5d6a7"], [0.4, "#81c784"], [0.55, "#66bb6a"], [0.7, "#4caf50"], [0.85, "#388e3c"], [1.0, "#1b5e20"]], "line": {"color": "#388e3c", "width": 0.5}, "opacity": 0.8, "size": 8, "symbol": "circle", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "projection": {"x": {"opacity": 0.7, "scale": 0.4, "show": true}, "y": {"opacity": 0.7, "scale": 0.4, "show": true}, "z": {"opacity": 0.7, "scale": 0.4, "show": true}}, "type": "scatter3d"}], "scattergl": [{"hovertemplate": "<b>X:</b> %{x}<br><b>Y:</b> %{y}<extra></extra>", "marker": {"color": "#4caf50", "line": {"color": "#388e3c", "width": 1}, "opacity": 0.8, "size": 10, "symbol": "circle", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "mode": "markers", "type": "scattergl"}], "scatterpolar": [{"fill": "toself", "fillcolor": "rgba(76, 175, 80, 0.2)", "hoveron": "points+fills", "line": {"color": "#4caf50", "shape": "spline", "smoothing": 0.3, "width": 2}, "marker": {"color": "#4caf50", "line": {"color": "#ffffff", "width": 1}, "size": 8, "symbol": "circle", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scatterpolar"}], "scatter": [{"fill": "tozer<PERSON>", "fillcolor": "rgba(76, 175, 80, 0.3)", "hoveron": "points+fills", "hovertemplate": "%{x}<br>%{y}<extra></extra>", "line": {"color": "#4caf50", "shape": "spline", "smoothing": 0.3, "width": 2}, "mode": "lines", "type": "scatter", "fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}], "sunburst": [{"insidetextorientation": "radial", "leaf": {"opacity": 0.9}, "marker": {"colorscale": [[0.0, "#e8f5e9"], [0.1, "#c8e6c9"], [0.25, "#a5d6a7"], [0.4, "#81c784"], [0.55, "#66bb6a"], [0.7, "#4caf50"], [0.85, "#388e3c"], [1.0, "#1b5e20"]], "line": {"color": "#ffffff", "width": 2}}, "textfont": {"color": "#ffffff", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "type": "sunburst"}], "surface": [{"colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "contours": {"x": {"color": "#e0e0e0", "show": true, "width": 1}, "y": {"color": "#e0e0e0", "show": true, "width": 1}, "z": {"color": "#e0e0e0", "show": true, "width": 1}}, "lighting": {"ambient": 0.5, "diffuse": 0.6, "fresnel": 0.4, "roughness": 0.5, "specular": 0.2}, "lightposition": {"x": -1000, "y": 1000, "z": 1000}, "type": "surface", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}], "table": [{"cells": {"align": ["left", "center"], "fill": {"color": "rgb(237,237,237)"}, "font": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "height": 30, "line": {"color": "white", "width": 1}, "values": []}, "header": {"align": ["left", "center"], "fill": {"color": "rgb(217,217,217)"}, "font": {"color": "#ffffff", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 13}, "height": 35, "line": {"color": "white", "width": 1}, "values": []}, "type": "table"}], "treemap": [{"marker": {"colorscale": [[0.0, "#e8f5e9"], [0.1, "#c8e6c9"], [0.25, "#a5d6a7"], [0.4, "#81c784"], [0.55, "#66bb6a"], [0.7, "#4caf50"], [0.85, "#388e3c"], [1.0, "#1b5e20"]], "line": {"color": "#ffffff", "width": 2}, "pad": {"b": 2, "l": 2, "r": 2, "t": 25}}, "pathbar": {"textfont": {"family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "thickness": 20, "visible": true}, "textfont": {"color": "#ffffff", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "textposition": "middle center", "type": "treemap"}], "violin": [{"box": {"visible": true}, "fillcolor": "#c8e6c9", "jitter": 0.05, "line": {"color": "#388e3c", "width": 2}, "marker": {"color": "#4caf50", "line": {"color": "#388e3c", "width": 1}, "opacity": 0.8}, "meanline": {"visible": true}, "opacity": 0.7, "points": "all", "scalemode": "width", "type": "violin"}], "waterfall": [{"connector": {"line": {"color": "#e0e0e0", "dash": "dot", "width": 2}}, "decreasing": {"marker": {"color": "#f44336", "line": {"color": "darkred", "width": 1}}}, "increasing": {"marker": {"color": "#4caf50", "line": {"color": "#388e3c", "width": 1}}}, "textfont": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}, "totals": {"marker": {"color": "#388e3c", "line": {"color": "#1b5e20", "width": 1}}}, "type": "waterfall"}], "barpolar": [{"marker": {"line": {"color": "rgb(237,237,237)", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "rgb(51,51,51)", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "rgb(51,51,51)"}, "baxis": {"endlinecolor": "rgb(51,51,51)", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "rgb(51,51,51)"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "type": "choropleth"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "type": "contourcarpet"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "type": "mesh3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scattergeo"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scattermapbox"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scattermap"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "type": "scatterternary"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#616161", "arrowhead": 0, "arrowsize": 1, "arrowwidth": 1, "bgcolor": "rgba(255, 255, 255, 0.9)", "bordercolor": "#d0d0d0", "borderpad": 4, "borderwidth": 1, "font": {"color": "#212121", "size": 11}}, "bargap": 0.2, "bargroupgap": 0.1, "colorscale": {"diverging": [[0.0, "#f44336"], [0.25, "#ffcdd2"], [0.5, "#ffffff"], [0.75, "#c8e6c9"], [1.0, "#388e3c"]], "sequential": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "sequentialminus": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]]}, "colorway": ["#F8766D", "#A3A500", "#00BF7D", "#00B0F6", "#E76BF3"], "dragmode": "zoom", "font": {"color": "rgb(51,51,51)", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 13}, "hoverlabel": {"align": "left", "bgcolor": "#00211b", "bordercolor": "#4caf50", "font": {"color": "#ffffff", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 11}}, "legend": {"bgcolor": "rgba(255, 255, 255, 0.95)", "bordercolor": "#d0d0d0", "borderwidth": 1, "font": {"color": "#212121", "size": 11}, "itemsizing": "constant", "itemwidth": 30, "orientation": "h", "tracegroupgap": 5, "traceorder": "normal", "x": 0, "xanchor": "left", "y": 1.02, "yanchor": "bottom"}, "margin": {"b": 60, "l": 80, "r": 60, "t": 80}, "modebar": {"activecolor": "#4caf50", "bgcolor": "rgba(255, 255, 255, 0.9)", "color": "#616161"}, "paper_bgcolor": "white", "plot_bgcolor": "rgb(237,237,237)", "selectdirection": "any", "title": {"font": {"color": "#212121", "family": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "size": 20}, "pad": {"b": 20, "t": 0}, "x": 0.5, "xanchor": "center", "y": 0.98, "yanchor": "top"}, "xaxis": {"gridcolor": "white", "griddash": "dot", "gridwidth": 1, "linecolor": "white", "linewidth": 1, "showgrid": true, "showline": true, "showspikes": true, "spikecolor": "#e0e0e0", "spikedash": "dot", "spikemode": "across", "spikethickness": 1, "tickcolor": "rgb(51,51,51)", "tickfont": {"color": "#616161", "size": 12}, "ticklen": 5, "ticks": "outside", "tickwidth": 1, "title": {"font": {"color": "#212121", "size": 16}, "standoff": 15}, "zeroline": true, "zerolinecolor": "white", "zerolinewidth": 1, "automargin": true}, "yaxis": {"gridcolor": "white", "gridwidth": 1, "linecolor": "white", "linewidth": 1, "showgrid": true, "showline": true, "showspikes": true, "spikecolor": "#e0e0e0", "spikedash": "dot", "spikemode": "across", "spikethickness": 1, "tickcolor": "rgb(51,51,51)", "tickfont": {"color": "#616161", "size": 12}, "ticklen": 5, "ticks": "outside", "tickwidth": 1, "title": {"font": {"color": "#212121", "size": 16}, "standoff": 15}, "zeroline": true, "zerolinecolor": "white", "zerolinewidth": 1, "automargin": true}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "rgb(237,237,237)", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hovermode": "closest", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}, "bgcolor": "rgb(237,237,237)", "radialaxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}}, "scene": {"xaxis": {"backgroundcolor": "rgb(237,237,237)", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "rgb(237,237,237)", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "rgb(237,237,237)", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside", "zerolinecolor": "white"}}, "shapedefaults": {"fillcolor": "black", "line": {"width": 0}, "opacity": 0.3}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}, "baxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}, "bgcolor": "rgb(237,237,237)", "caxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}}}}, "title": {"text": "Invoiced Energy Consumption by Site for 2024 (All Energy Types)"}, "xaxis": {"anchor": "y", "domain": [0.0, 1.0], "tickangle": -45, "title": {"text": "Site"}}, "yaxis": {"anchor": "x", "domain": [0.0, 1.0], "title": {"text": "Invoiced Consumption (kWh)"}}, "autosize": true}}