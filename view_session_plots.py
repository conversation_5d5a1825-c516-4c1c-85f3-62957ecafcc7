#!/usr/bin/env python3
"""
Script to view all plots for a given session ID.
Excludes plots that start with 'plotly_plotly' and displays them in the browser.

Usage:
    python view_session_plots.py <session_id>
    
Example:
    python view_session_plots.py 07d7ea02-ca2b-4874-9760-ce881ba454ab
"""

import sys
import os
import pickle
import asyncio
import argparse
from pathlib import Path
from typing import List, Dict, Any
import webbrowser
import tempfile

# Add the current directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from sandbox_client import SandboxClient
    from plot_template import apply_company_style
    import plotly.graph_objects as go
    import plotly.offline as pyo
    from plotly.subplots import make_subplots
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure you have plotly installed: pip install plotly")
    sys.exit(1)


class SessionPlotViewer:
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.sandbox_client = SandboxClient()
        self.plots_data: List[Dict[str, Any]] = []
    
    async def fetch_session_info(self) -> Dict[str, Any]:
        """Fetch session information to get available plots."""
        try:
            session_info = await self.sandbox_client.get_session_info(self.session_id)
            if "error" in session_info:
                raise Exception(f"Session error: {session_info['error']}")
            return session_info
        except Exception as e:
            raise Exception(f"Failed to fetch session info: {str(e)}")
    
    async def get_plot_list(self) -> List[str]:
        """Get list of plot files for the session."""
        session_info = await self.fetch_session_info()
        
        # Get plots from session info
        plots = session_info.get("plots", [])
        
        # Filter out plots that start with 'plotly_plotly'
        filtered_plots = [
            plot for plot in plots 
            if not plot.startswith("plotly_plotly")
            and plot.endswith(".pickle")
            and ("plotly" in plot.lower() or "fig" in plot.lower())
        ]
        
        print(f"Found {len(plots)} total plots, {len(filtered_plots)} after filtering")
        print(f"Filtered plots: {filtered_plots}")
        
        return filtered_plots
    
    async def download_plot(self, plot_name: str) -> go.Figure:
        """Download and deserialize a plot."""
        try:
            print(f"Downloading plot: {plot_name}")
            plot_bytes = await self.sandbox_client.download_plot(
                session_id=self.session_id,
                plot_name=plot_name
            )
            
            if not plot_bytes:
                raise Exception(f"No data received for plot {plot_name}")
            
            # Deserialize the pickle
            fig = pickle.loads(plot_bytes)
            
            if not isinstance(fig, go.Figure):
                raise Exception(f"Plot {plot_name} is not a Plotly Figure object")
            
            # Apply company styling
            fig = apply_company_style(fig)
            
            return fig
            
        except Exception as e:
            print(f"Error downloading plot {plot_name}: {str(e)}")
            return None
    
    async def fetch_all_plots(self) -> List[Dict[str, Any]]:
        """Fetch all plots for the session."""
        plot_names = await self.get_plot_list()
        
        if not plot_names:
            print("No plots found for this session.")
            return []
        
        plots_data = []
        
        for plot_name in plot_names:
            fig = await self.download_plot(plot_name)
            if fig:
                plots_data.append({
                    "name": plot_name,
                    "figure": fig,
                    "title": plot_name.replace(".pickle", "").replace("_", " ").title()
                })
        
        return plots_data
    
    def create_combined_view(self, plots_data: List[Dict[str, Any]]) -> go.Figure:
        """Create a combined view with all plots in subplots."""
        if not plots_data:
            return None
        
        if len(plots_data) == 1:
            # Single plot - return as is with updated title
            fig = plots_data[0]["figure"]
            fig.update_layout(
                title=f"Session {self.session_id[:8]}... - {plots_data[0]['title']}",
                title_x=0.5
            )
            return fig
        
        # Multiple plots - create subplots
        num_plots = len(plots_data)
        
        # Calculate grid dimensions
        if num_plots <= 2:
            rows, cols = 1, num_plots
        elif num_plots <= 4:
            rows, cols = 2, 2
        elif num_plots <= 6:
            rows, cols = 2, 3
        else:
            rows = (num_plots + 2) // 3
            cols = 3
        
        # Create subplot titles
        subplot_titles = [plot["title"] for plot in plots_data]
        
        # Create subplots
        fig = make_subplots(
            rows=rows, 
            cols=cols,
            subplot_titles=subplot_titles,
            horizontal_spacing=0.1,
            vertical_spacing=0.15
        )
        
        # Add each plot to the subplots
        for i, plot_data in enumerate(plots_data):
            row = (i // cols) + 1
            col = (i % cols) + 1
            
            source_fig = plot_data["figure"]
            
            # Add all traces from the source figure
            for trace in source_fig.data:
                fig.add_trace(trace, row=row, col=col)
        
        # Update layout
        fig.update_layout(
            title=f"Session {self.session_id[:8]}... - All Plots ({num_plots} plots)",
            title_x=0.5,
            showlegend=False,  # Disable legend for cleaner look with multiple plots
            height=300 * rows  # Adjust height based on number of rows
        )
        
        # Apply company styling
        fig = apply_company_style(fig)
        
        return fig
    
    def save_and_open_html(self, fig: go.Figure) -> str:
        """Save the figure as HTML and open in browser."""
        if not fig:
            print("No figure to display.")
            return None
        
        # Create a temporary HTML file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            html_file = f.name
        
        # Generate HTML
        html_content = pyo.plot(
            fig, 
            output_type='div', 
            include_plotlyjs=True,
            config={'displayModeBar': True, 'responsive': True}
        )
        
        # Wrap in a complete HTML document
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Session Plots - {self.session_id[:8]}...</title>
            <meta charset="utf-8">
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    background-color: #f5f5f5;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 20px;
                    padding: 20px;
                    background-color: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                .plot-container {{
                    background-color: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    padding: 20px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Session Plots Viewer</h1>
                <p><strong>Session ID:</strong> {self.session_id}</p>
                <p><strong>Number of Plots:</strong> {len(self.plots_data)}</p>
            </div>
            <div class="plot-container">
                {html_content}
            </div>
        </body>
        </html>
        """
        
        # Write to file
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(full_html)
        
        print(f"HTML file saved to: {html_file}")
        
        # Open in browser
        webbrowser.open(f'file://{html_file}')
        print("Opening in browser...")
        
        return html_file
    
    async def view_plots(self):
        """Main method to fetch and display all plots."""
        try:
            print(f"Fetching plots for session: {self.session_id}")
            
            # Fetch all plots
            self.plots_data = await self.fetch_all_plots()
            
            if not self.plots_data:
                print("No plots found to display.")
                return
            
            print(f"Successfully fetched {len(self.plots_data)} plots")
            
            # Create combined view
            combined_fig = self.create_combined_view(self.plots_data)
            
            # Save and open in browser
            html_file = self.save_and_open_html(combined_fig)
            
            print(f"\nPlots displayed successfully!")
            print(f"HTML file: {html_file}")
            print(f"You can keep this file open or delete it when done.")
            
        except Exception as e:
            print(f"Error viewing plots: {str(e)}")
            import traceback
            traceback.print_exc()


async def main():
    parser = argparse.ArgumentParser(
        description="View all plots for a given session ID",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python view_session_plots.py 07d7ea02-ca2b-4874-9760-ce881ba454ab
  python view_session_plots.py abc123def456
        """
    )
    
    parser.add_argument(
        "session_id",
        help="The session ID to fetch plots for"
    )
    
    parser.add_argument(
        "--list-only",
        action="store_true",
        help="Only list available plots without displaying them"
    )
    
    args = parser.parse_args()
    
    if not args.session_id:
        print("Error: Session ID is required")
        parser.print_help()
        sys.exit(1)
    
    viewer = SessionPlotViewer(args.session_id)
    
    if args.list_only:
        try:
            plots = await viewer.get_plot_list()
            print(f"\nAvailable plots for session {args.session_id}:")
            for i, plot in enumerate(plots, 1):
                print(f"  {i}. {plot}")
        except Exception as e:
            print(f"Error listing plots: {str(e)}")
    else:
        await viewer.view_plots()


if __name__ == "__main__":
    asyncio.run(main())
