oksigen:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, water, etc.).
      mode: in
      indicator_options:
        energie_facturante_grdf: "to get the electric consumption data of 'grdf' meters"
        conso_mensuelle_enedis: "to get the electric consumption data of 'enedis' meters"
    - id: load_curve
      description: |
        Uses the load curve service to get the detailed time series of electric consumption data for few or one site and use it to get high granularity data like 1 hour or less.
      mode: out
      indicator_options:
        total-power_supply: "to get time series of the total power used by the site for each timestamp"
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"
engie_ec_ent_em:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, water, etc.).
      mode: in
      indicator_options:
        enedis_dm: "to get the electric consumption data of 'enedis_dm' meters"
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        cdc_enedis: "to get the electric power data of 'cdc_enedis' meters"
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"
engie_ec_gc_prive:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, water, etc.).
      mode: in
      indicator_options:
        enedis_dm: "to get the electric consumption data of 'enedis_dm' meters"
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        cdc_enedis: "to get the electric power data of 'cdc_enedis' meters"
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"   
engie_ec_gc_publics:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, water, etc.).
      mode: in
      indicator_options:
        enedis_dm: "to get the electric consumption data of 'enedis_dm' meters"
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        cdc_enedis: "to get the electric power data of 'cdc_enedis' meters"
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"             
siplec:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, water, etc.).
      mode: in
      indicator_options:
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        conso_mensuelle_enedis: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
        puissance_enedis: "to get the electric power data of 'puissance_enedis' meters"
    - id: invoicing_statistics
      description: |
        This algorithm normalises and computes data from energy supplier invoices 
        to return total or detailed consumptions and costs over a given time range.
        **IMPRORTANT**: when choosing the indicators (except for the ones that contains elec like invoice.elec_cost_cmdps) You must append the fluid code after a dash (-). 
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL,PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY.
      mode: out
      indicator_options:
        invoice.cost_consumption: "Cost associated with gas or electricity consumption"
        invoice.elec_cost_cmdps: "Cost related to electricity power exceedance"
        invoice.consumption_invoiced: "Energy consumption (for energy types ex: elec, gas, ..) in kWh as stated in the invoice, to use when there is no, fluid precised. "
        invoice.elec_consumption: >
          Represents the electricity consumption in kWh during a specific time period. 
          You must append one of the following period suffixes:
          _peak_hour, _winter_peak_hour, _winter_off_peak_hour, _summer_peak_hour, 
          _summer_off_peak_hour, _single_period, _full_hour, _off_peak_hour, 
          _half_season_peak_hour, _half_season_off_peak_hour, _july_august, 
          _white_full_hour, _white_off_peak_hour, _red_full_hour, _red_off_peak_hour, 
          _blue_full_hour, _blue_off_peak_hour.
        emission_invoiced: >
          To get emissions data. You must append the fluid code after a dash (-). 
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL, ALLW.
        ghg_ktco2: >
          Indicator used to measure and track greenhouse gas emissions (in ktCO₂).
          You must append the fluid code after a dash (-).
        invoice.elec_cost: >
          Represents the electricity cost during a specific time period. 
          You must append one of the following period suffixes:
          _peak_hour, _winter_peak_hour, _winter_off_peak_hour, _summer_peak_hour, 
          _summer_off_peak_hour, _single_period, _full_hour, _off_peak_hour, 
          _half_season_peak_hour, _half_season_off_peak_hour, _july_august, 
          _white_full_hour, _white_off_peak_hour, _red_full_hour, _red_off_peak_hour, 
          _blue_full_hour, _blue_off_peak_hour.
        invoice.cost_total_exc_vat: "Total cost excluding VAT"
        invoice.cost_total_inc_vat: "Total cost including VAT"
        invoice.date: "Invoice date"
        invoice.delivery_point: "Delivery point or PDL"
        invoice.invoicing_start_date: "Start date of the invoicing period"
        invoice.invoicing_end_date: "End date of the invoicing period"
        invoice.cost_total_vat: "Total VAT amount invoiced"
        invoice.cost_obligation_cee: "Cost related to CEE obligations"
        invoice.price_consumption: "Electricity unit price per kWh"
        invoice.cost_fixed_charge: "Fixed subscription cost"
        invoice.cost_supply: "Transmission/delivery cost"
        invoice.cost_tax: "Total tax amount"    
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"
veolia:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, water, etc.).
      mode: in
      indicator_options:
        conso_Strom: "to get the electric consumption data of 'conso_Strom' meters"
        conso_Trinkwasser: "to get the electric consumption data of 'conso_Trinkwasser' meters"
        conso_Fabrikwasser: "to get the electric consumption data of 'conso_Fabrikwasser' meters"
        conso_WAI hydraulisch: "to get the electric consumption data of 'conso_WAI hydraulisch' meters"
        conso_Dampf: "to get the electric consumption data of 'conso_Dampf' meters"
        conso_Steuerluft: "to get the electric consumption data of 'conso_Steuerluft' meters"
        conso_WBI: "to get the electric consumption data of 'conso_WBI' meters"
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"
cyrisea_cd92:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        conso_mensuelle_enedis: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
        energie_facturante_grdf: "to get the gas consumption data of 'energie_facturante_grdf' meters"
    - id: invoicing_statistics
      description: |
        This algorithm normalises and computes data from energy supplier invoices 
        to return total or detailed consumptions and costs over a given time range.
        **IMPRORTANT**: when choosing the indicators (except for the ones that contains elec like invoice.elec_cost_cmdps) You must append the fluid code after a dash (-). 
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL,PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY.
      mode: out
      indicator_options:
        invoice.cost_consumption: "Cost associated with gas or electricity consumption"
        invoice.elec_cost_cmdps: "Cost related to electricity power exceedance"
        invoice.consumption_invoiced: "Energy consumption (for energy types ex: elec, gas, ..) in kWh as stated in the invoice, to use when there is no, fluid precised. "
        invoice.elec_consumption: >
          Represents the electricity consumption in kWh during a specific time period. 
          You must append one of the following period suffixes:
          _peak_hour, _winter_peak_hour, _winter_off_peak_hour, _summer_peak_hour, 
          _summer_off_peak_hour, _single_period, _full_hour, _off_peak_hour, 
          _half_season_peak_hour, _half_season_off_peak_hour, _july_august, 
          _white_full_hour, _white_off_peak_hour, _red_full_hour, _red_off_peak_hour, 
          _blue_full_hour, _blue_off_peak_hour.
        emission_invoiced: >
          To get emissions data. You must append the fluid code after a dash (-). 
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL, ALLW.
        ghg_ktco2: >
          Indicator used to measure and track greenhouse gas emissions (in ktCO₂).
          You must append the fluid code after a dash (-).
        invoice.elec_cost: >
          Represents the electricity cost during a specific time period. 
          You must append one of the following period suffixes:
          _peak_hour, _winter_peak_hour, _winter_off_peak_hour, _summer_peak_hour, 
          _summer_off_peak_hour, _single_period, _full_hour, _off_peak_hour, 
          _half_season_peak_hour, _half_season_off_peak_hour, _july_august, 
          _white_full_hour, _white_off_peak_hour, _red_full_hour, _red_off_peak_hour, 
          _blue_full_hour, _blue_off_peak_hour.
        invoice.cost_total_exc_vat: "Total cost excluding VAT"
        invoice.cost_total_inc_vat: "Total cost including VAT"
        invoice.date: "Invoice date"
        invoice.delivery_point: "Delivery point or PDL"
        invoice.invoicing_start_date: "Start date of the invoicing period"
        invoice.invoicing_end_date: "End date of the invoicing period"
        invoice.cost_total_vat: "Total VAT amount invoiced"
        invoice.cost_obligation_cee: "Cost related to CEE obligations"
        invoice.price_consumption: "Electricity unit price per kWh"
        invoice.cost_fixed_charge: "Fixed subscription cost"
        invoice.cost_supply: "Transmission/delivery cost"
        invoice.cost_tax: "Total tax amount"
    - id: commitment
      description: |
        This algorithm aims at computing energy performance indicators defined in a commitment.
        Commitments are linked to a specific site and can be aggregated by type to compute average performance
        if the user requests global results across commitments.

        The performance is defined by the indicator name within the commitment.
      mode: in
      indicator_options:
        COM_1.1: >
          === COM Mode ===
          • Used to compute global/aggregated performance over multiple commitments at a site.

          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N’B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l’usage chauffage.
              - 'contract-N’B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - "formula": "{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N’B}"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum   
    - id: profit_sharing_multisite
      description: |
        This algorithms aims at giving the client and operator profit (intéressement) of a commitment. 
        The profit can be returned for a specific commitment or by site for a commitment.
      mode: out
      formula:
        Intéressement exploitant : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
        Intéressement client : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"            
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"
alterea:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        conso_mensuelle_enedis: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
        energie_facturante_grdf: "to get the gas consumption data of 'energie_facturante_grdf' meters"
    - id: invoicing_statistics
      description: |
        This algorithm normalises and computes data from energy supplier invoices 
        to return total or detailed consumptions and costs over a given time range.
        **IMPRORTANT**: when choosing the indicators (except for the ones that contains elec like invoice.elec_cost_cmdps) You must append the fluid code after a dash (-). 
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL,PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY.
      mode: out
      indicator_options:
        invoice.cost_consumption: "Cost associated with gas or electricity consumption"
        invoice.elec_cost_cmdps: "Cost related to electricity power exceedance"
        invoice.consumption_invoiced: "Energy consumption (for energy types ex: elec, gas, ..) in kWh as stated in the invoice, to use when there is no, fluid precised. "
        invoice.elec_consumption: >
          Represents the electricity consumption in kWh during a specific time period. 
          You must append one of the following period suffixes:
          _peak_hour, _winter_peak_hour, _winter_off_peak_hour, _summer_peak_hour, 
          _summer_off_peak_hour, _single_period, _full_hour, _off_peak_hour, 
          _half_season_peak_hour, _half_season_off_peak_hour, _july_august, 
          _white_full_hour, _white_off_peak_hour, _red_full_hour, _red_off_peak_hour, 
          _blue_full_hour, _blue_off_peak_hour.
        emission_invoiced: >
          To get emissions data. You must append the fluid code after a dash (-). 
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL, ALLW.
        ghg_ktco2: >
          Indicator used to measure and track greenhouse gas emissions (in ktCO₂).
          You must append the fluid code after a dash (-).
        invoice.elec_cost: >
          Represents the electricity cost during a specific time period. 
          You must append one of the following period suffixes:
          _peak_hour, _winter_peak_hour, _winter_off_peak_hour, _summer_peak_hour, 
          _summer_off_peak_hour, _single_period, _full_hour, _off_peak_hour, 
          _half_season_peak_hour, _half_season_off_peak_hour, _july_august, 
          _white_full_hour, _white_off_peak_hour, _red_full_hour, _red_off_peak_hour, 
          _blue_full_hour, _blue_off_peak_hour.
        invoice.cost_total_exc_vat: "Total cost excluding VAT"
        invoice.cost_total_inc_vat: "Total cost including VAT"
        invoice.date: "Invoice date"
        invoice.delivery_point: "Delivery point or PDL"
        invoice.invoicing_start_date: "Start date of the invoicing period"
        invoice.invoicing_end_date: "End date of the invoicing period"
        invoice.cost_total_vat: "Total VAT amount invoiced"
        invoice.cost_obligation_cee: "Cost related to CEE obligations"
        invoice.price_consumption: "Electricity unit price per kWh"
        invoice.cost_fixed_charge: "Fixed subscription cost"
        invoice.cost_supply: "Transmission/delivery cost"
        invoice.cost_tax: "Total tax amount"
    - id: commitment
      description: |
        This algorithm aims at computing energy performance indicators defined in a commitment.
        Commitments are linked to a specific site and can be aggregated by type to compute average performance
        if the user requests global results across commitments.

        The performance is defined by the indicator name within the commitment.
      mode: in
      indicator_options:
        COM_1.1: >
          === COM Mode ===
          • Used to compute global/aggregated performance over multiple commitments at a site.

          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N’B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l’usage chauffage.
              - 'contract-N’B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - "formula": "{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N’B}"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum    
    - id: profit_sharing_multisite
      description: |
        This algorithms aims at giving the client and operator profit (intéressement) of a commitment. 
        The profit can be returned for a specific commitment or by site for a commitment.
      mode: out
      formula:
        Intéressement exploitant : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
        Intéressement client : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"                          
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"        
cyrisea_cd74:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        conso_mensuelle_enedis: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
        energie_facturante_grdf: "to get the gas consumption data of 'energie_facturante_grdf' meters"
    - id: invoicing_statistics
      description: |
        This algorithm normalises and computes data from energy supplier invoices 
        to return total or detailed consumptions and costs over a given time range.
        **IMPRORTANT**: when choosing the indicators (except for the ones that contains elec like invoice.elec_cost_cmdps) You must append the fluid code after a dash (-). 
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL,PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY.
      mode: out
      indicator_options:
        invoice.cost_consumption: "Cost associated with gas or electricity consumption"
        invoice.elec_cost_cmdps: "Cost related to electricity power exceedance"
        invoice.consumption_invoiced: "Energy consumption (for energy types ex: elec, gas, ..) in kWh as stated in the invoice, to use when there is no, fluid precised. "
        invoice.elec_consumption: >
          Represents the electricity consumption in kWh during a specific time period. 
          You must append one of the following period suffixes:
          _peak_hour, _winter_peak_hour, _winter_off_peak_hour, _summer_peak_hour, 
          _summer_off_peak_hour, _single_period, _full_hour, _off_peak_hour, 
          _half_season_peak_hour, _half_season_off_peak_hour, _july_august, 
          _white_full_hour, _white_off_peak_hour, _red_full_hour, _red_off_peak_hour, 
          _blue_full_hour, _blue_off_peak_hour.
        emission_invoiced: >
          To get emissions data. You must append the fluid code after a dash (-). 
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL, ALLW.
        ghg_ktco2: >
          Indicator used to measure and track greenhouse gas emissions (in ktCO₂).
          You must append the fluid code after a dash (-).
        invoice.elec_cost: >
          Represents the electricity cost during a specific time period. 
          You must append one of the following period suffixes:
          _peak_hour, _winter_peak_hour, _winter_off_peak_hour, _summer_peak_hour, 
          _summer_off_peak_hour, _single_period, _full_hour, _off_peak_hour, 
          _half_season_peak_hour, _half_season_off_peak_hour, _july_august, 
          _white_full_hour, _white_off_peak_hour, _red_full_hour, _red_off_peak_hour, 
          _blue_full_hour, _blue_off_peak_hour.
        invoice.cost_total_exc_vat: "Total cost excluding VAT"
        invoice.cost_total_inc_vat: "Total cost including VAT"
        invoice.date: "Invoice date"
        invoice.delivery_point: "Delivery point or PDL"
        invoice.invoicing_start_date: "Start date of the invoicing period"
        invoice.invoicing_end_date: "End date of the invoicing period"
        invoice.cost_total_vat: "Total VAT amount invoiced"
        invoice.cost_obligation_cee: "Cost related to CEE obligations"
        invoice.price_consumption: "Electricity unit price per kWh"
        invoice.cost_fixed_charge: "Fixed subscription cost"
        invoice.cost_supply: "Transmission/delivery cost"
        invoice.cost_tax: "Total tax amount"
    - id: commitment
      description: |
        This algorithm aims at computing energy performance indicators defined in a commitment.
        Commitments are linked to a specific site and can be aggregated by type to compute average performance
        if the user requests global results across commitments.

        The performance is defined by the indicator name within the commitment.
      mode: in
      indicator_options:
        COM_1.1: >
          === COM Mode ===
          • Used to compute global/aggregated performance over multiple commitments at a site.

          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N’B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l’usage chauffage.
              - 'contract-N’B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - "formula": "{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N’B}"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum    
    - id: profit_sharing_multisite
      description: |
        This algorithms aims at giving the client and operator profit (intéressement) of a commitment. 
        The profit can be returned for a specific commitment or by site for a commitment.
      mode: out
      formula:
        Intéressement exploitant : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
        Intéressement client : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"                          
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"           
groupeherve_alerteo:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_10min_enedis: "to get the electric consumption data of 'conso_10min_enedis' meters"
        conso_mensuelle_enedis: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
        energie_facturante_grdf: "to get the gas consumption data of 'energie_facturante_grdf' meters"
        energie_info_grdf: "to get the gas consumption data of 'energie_info_grdf' meters"
    - id: consumption_meter_supply
      description: |
        Compute the consumption based on meter supply.
        The fluid has to be given in input. Depending on it, consumption is returned in:
          - m³ for WATER
          - kWh for all other fluids
          
        For fluids GAS, WOOD, FOL, FOD, COAL, PROPANE, or LPG, normalized consumption is available
        in PCI (lower heating value) and PCS (higher heating value).

        Consumption can be returned with or without linearization (prorata).
      mode: out
      indicator_options:
        consumption_meter_supply: >
          This is a flexible indicator name and must be composed with parts:
          
          • Fluid code (**mandatory**):  
            Must be appended after a dash (-).  
            Example: `consumption_meter_supply-ELEC`
          
          • Fluid option (**optional**):  
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.  
            Example: `consumption_meter_supply_pci-GAS`
          
          • Prorata option (**optional**):  
            Can be `no_prorata`, appended after another dash following the fluid code.  
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`

          Valid fluid codes: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL. 
    - id: commitment
      description: |
        This algorithm aims at computing energy performance indicators defined in a commitment.
        Commitments are linked to a specific site and can be aggregated by type to compute average performance
        if the user requests global results across commitments.

        The performance is defined by the indicator name within the commitment.
      mode: out
      indicator_options:
        COM_1.1: >
          === COM Mode ===
          • Used to compute global/aggregated performance over multiple commitments at a site.

          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N’B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l’usage chauffage.
              - 'contract-N’B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - "formula": "{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N’B}"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum    
    - id: profit_sharing_multisite
      description: |
        This algorithms aims at giving the client and operator profit (intéressement) of a commitment. 
        The profit can be returned for a specific commitment or by site for a commitment.
      mode: out
      formula:
        Intéressement exploitant : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
        Intéressement client : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"                       
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"
groupeherve_hervethemique:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_10min_enedis: "to get the electric consumption data of 'conso_10min_enedis' meters"
        conso_mensuelle_enedis: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
        energie_facturante_grdf: "to get the gas consumption data of 'energie_facturante_grdf' meters"
        energie_info_grdf: "to get the gas consumption data of 'energie_info_grdf' meters"
    - id: consumption_meter_supply
      description: |
        Compute the consumption based on meter supply.
        The fluid has to be given in input. Depending on it, consumption is returned in:
          - m³ for WATER
          - kWh for all other fluids
          
        For fluids GAS, WOOD, FOL, FOD, COAL, PROPANE, or LPG, normalized consumption is available
        in PCI (lower heating value) and PCS (higher heating value).

        Consumption can be returned with or without linearization (prorata).
      mode: out
      indicator_options:
        consumption_meter_supply: >
          This is a flexible indicator name and must be composed with parts:
          
          • Fluid code (**mandatory**):  
            Must be appended after a dash (-).  
            Example: `consumption_meter_supply-ELEC`
          
          • Fluid option (**optional**):  
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.  
            Example: `consumption_meter_supply_pci-GAS`
          
          • Prorata option (**optional**):  
            Can be `no_prorata`, appended after another dash following the fluid code.  
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`

          Valid fluid codes: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL.  
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        conso_mensuelle_enedis: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
        energie_facturante_grdf: "to get the gas consumption data of 'energie_facturante_grdf' meters"
    - id: invoicing_statistics
      description: |
        This algorithm normalises and computes data from energy supplier invoices 
        to return total or detailed consumptions and costs over a given time range.
        **IMPRORTANT**: when choosing the indicators (except for the ones that contains elec like invoice.elec_cost_cmdps) You must append the fluid code after a dash (-). 
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL,PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY.
      mode: out
      indicator_options:
        invoice.cost_consumption: "Cost associated with gas or electricity consumption"
        invoice.elec_cost_cmdps: "Cost related to electricity power exceedance"
        invoice.consumption_invoiced: "Energy consumption (for energy types ex: elec, gas, ..) in kWh as stated in the invoice, to use when there is no, fluid precised. "
        invoice.elec_consumption: >
          Represents the electricity consumption in kWh during a specific time period. 
          You must append one of the following period suffixes:
          _peak_hour, _winter_peak_hour, _winter_off_peak_hour, _summer_peak_hour, 
          _summer_off_peak_hour, _single_period, _full_hour, _off_peak_hour, 
          _half_season_peak_hour, _half_season_off_peak_hour, _july_august, 
          _white_full_hour, _white_off_peak_hour, _red_full_hour, _red_off_peak_hour, 
          _blue_full_hour, _blue_off_peak_hour.
        emission_invoiced: >
          To get emissions data. You must append the fluid code after a dash (-). 
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL, ALLW.
        ghg_ktco2: >
          Indicator used to measure and track greenhouse gas emissions (in ktCO₂).
          You must append the fluid code after a dash (-).
        invoice.elec_cost: >
          Represents the electricity cost during a specific time period. 
          You must append one of the following period suffixes:
          _peak_hour, _winter_peak_hour, _winter_off_peak_hour, _summer_peak_hour, 
          _summer_off_peak_hour, _single_period, _full_hour, _off_peak_hour, 
          _half_season_peak_hour, _half_season_off_peak_hour, _july_august, 
          _white_full_hour, _white_off_peak_hour, _red_full_hour, _red_off_peak_hour, 
          _blue_full_hour, _blue_off_peak_hour.
        invoice.cost_total_exc_vat: "Total cost excluding VAT"
        invoice.cost_total_inc_vat: "Total cost including VAT"
        invoice.date: "Invoice date"
        invoice.delivery_point: "Delivery point or PDL"
        invoice.invoicing_start_date: "Start date of the invoicing period"
        invoice.invoicing_end_date: "End date of the invoicing period"
        invoice.cost_total_vat: "Total VAT amount invoiced"
        invoice.cost_obligation_cee: "Cost related to CEE obligations"
        invoice.price_consumption: "Electricity unit price per kWh"
        invoice.cost_fixed_charge: "Fixed subscription cost"
        invoice.cost_supply: "Transmission/delivery cost"
        invoice.cost_tax: "Total tax amount"
    - id: commitment
      description: |
        This algorithm aims at computing energy performance indicators defined in a commitment.
        Commitments are linked to a specific site and can be aggregated by type to compute average performance
        if the user requests global results across commitments.

        The performance is defined by the indicator name within the commitment.
      mode: out
      indicator_options:
        COM_1.1: >
          === COM Mode ===
          • Used to compute global/aggregated performance over multiple commitments at a site.

          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N’B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l’usage chauffage.
              - 'contract-N’B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - "formula": "{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N’B}"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum   
    - id: profit_sharing_multisite
      description: |
        This algorithms aims at giving the client and operator profit (intéressement) of a commitment. 
        The profit can be returned for a specific commitment or by site for a commitment.
      mode: out
      formula:
        Intéressement exploitant : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
        Intéressement client : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"                          
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"     
groupeherve_agences:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_10min_enedis: "to get the electric consumption data of 'conso_10min_enedis' meters"
        conso_mensuelle_enedis: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
        energie_facturante_grdf: "to get the gas consumption data of 'energie_facturante_grdf' meters"
        energie_info_grdf: "to get the gas consumption data of 'energie_info_grdf' meters"
    - id: consumption_meter_supply
      description: |
        Compute the consumption based on meter supply.
        The fluid has to be given in input. Depending on it, consumption is returned in:
          - m³ for WATER
          - kWh for all other fluids
          
        For fluids GAS, WOOD, FOL, FOD, COAL, PROPANE, or LPG, normalized consumption is available
        in PCI (lower heating value) and PCS (higher heating value).

        Consumption can be returned with or without linearization (prorata).
      mode: out
      indicator_options:
        consumption_meter_supply: >
          This is a flexible indicator name and must be composed with parts:
          
          • Fluid code (**mandatory**):  
            Must be appended after a dash (-).  
            Example: `consumption_meter_supply-ELEC`
          
          • Fluid option (**optional**):  
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.  
            Example: `consumption_meter_supply_pci-GAS`
          
          • Prorata option (**optional**):  
            Can be `no_prorata`, appended after another dash following the fluid code.  
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`

          Valid fluid codes: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL.  
    - id: commitment
      description: |
        This algorithm aims at computing energy performance indicators defined in a commitment.
        Commitments are linked to a specific site and can be aggregated by type to compute average performance
        if the user requests global results across commitments.

        The performance is defined by the indicator name within the commitment.
      mode: out
      indicator_options:
        COM_1.1: >
          === COM Mode ===
          • Used to compute global/aggregated performance over multiple commitments at a site.

          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N’B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l’usage chauffage.
              - 'contract-N’B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - "formula": "{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N’B}"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum   
    - id: profit_sharing_multisite
      description: |
        This algorithms aims at giving the client and operator profit (intéressement) of a commitment. 
        The profit can be returned for a specific commitment or by site for a commitment.
      mode: out
      formula:
        Intéressement exploitant : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
        Intéressement client : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"                         
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"           
demathieu_bard:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids(e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        conso_elec: "to get the electric consumption data of 'conso_elec' meters"   
    - id: consumption_meter_supply
      description: |
        Compute the consumption based on meter supply.
        The fluid has to be given in input. Depending on it, consumption is returned in:
          - m³ for WATER
          - kWh for all other fluids
          
        For fluids GAS, WOOD, FOL, FOD, COAL, PROPANE, or LPG, normalized consumption is available
        in PCI (lower heating value) and PCS (higher heating value).

        Consumption can be returned with or without linearization (prorata).
      mode: out
      indicator_options:
        consumption_meter_supply: >
          This is a flexible indicator name and must be composed with parts:
          
          • Fluid code (**mandatory**):  
            Must be appended after a dash (-).  
            Example: `consumption_meter_supply-ELEC`
          
          • Fluid option (**optional**):  
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.  
            Example: `consumption_meter_supply_pci-GAS`
          
          • Prorata option (**optional**):  
            Can be `no_prorata`, appended after another dash following the fluid code.  
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`

          Valid fluid codes: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL.        
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"     
demathieu_bard_chantiers:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids(e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        conso_elec: "to get the electric consumption data of 'conso_elec' meters"   
    - id: consumption_meter_supply
      description: |
        Compute the consumption based on meter supply.
        The fluid has to be given in input. Depending on it, consumption is returned in:
          - m³ for WATER
          - kWh for all other fluids
          
        For fluids GAS, WOOD, FOL, FOD, COAL, PROPANE, or LPG, normalized consumption is available
        in PCI (lower heating value) and PCS (higher heating value).

        Consumption can be returned with or without linearization (prorata).
      mode: out
      indicator_options:
        consumption_meter_supply: >
          This is a flexible indicator name and must be composed with parts:
          
          • Fluid code (**mandatory**):  
            Must be appended after a dash (-).  
            Example: `consumption_meter_supply-ELEC`
          
          • Fluid option (**optional**):  
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.  
            Example: `consumption_meter_supply_pci-GAS`
          
          • Prorata option (**optional**):  
            Can be `no_prorata`, appended after another dash following the fluid code.  
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`

          Valid fluid codes: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL.        
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"  
demathieu_bard_patrimoine:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids(e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        conso_elec: "to get the electric consumption data of 'conso_elec' meters"   
    - id: consumption_meter_supply
      description: |
        Compute the consumption based on meter supply.
        The fluid has to be given in input. Depending on it, consumption is returned in:
          - m³ for WATER
          - kWh for all other fluids
          
        For fluids GAS, WOOD, FOL, FOD, COAL, PROPANE, or LPG, normalized consumption is available
        in PCI (lower heating value) and PCS (higher heating value).

        Consumption can be returned with or without linearization (prorata).
      mode: out
      indicator_options:
        consumption_meter_supply: >
          This is a flexible indicator name and must be composed with parts:
          
          • Fluid code (**mandatory**):  
            Must be appended after a dash (-).  
            Example: `consumption_meter_supply-ELEC`
          
          • Fluid option (**optional**):  
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.  
            Example: `consumption_meter_supply_pci-GAS`
          
          • Prorata option (**optional**):  
            Can be `no_prorata`, appended after another dash following the fluid code.  
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`

          Valid fluid codes: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL.        
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"                              
vincienergies:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        conso_mensuelle_enedis: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
        energie_facturante_grdf: "to get the gas consumption data of 'energie_facturante_grdf' meters" 
    - id: commitment
      description: |
        This algorithm aims at computing energy performance indicators defined in a commitment.
        Commitments are linked to a specific site and can be aggregated by type to compute average performance
        if the user requests global results across commitments.

        The performance is defined by the indicator name within the commitment.
      mode: in
      indicator_options:
        COM_1.1: >
          === COM Mode ===
          • Used to compute global/aggregated performance over multiple commitments at a site.

          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N’B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l’usage chauffage.
              - 'contract-N’B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - "formula": "{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N’B}"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum  .       
    - id: profit_sharing_multisite
      description: |
        This algorithms aims at giving the client and operator profit (intéressement) of a commitment. 
        The profit can be returned for a specific commitment or by site for a commitment.
      mode: out
      formula:
        Intéressement exploitant : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
        Intéressement client : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"                      
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"           
helexia:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, electric power, gas, water, etc.).
      mode: in
      indicator_options:
        conso_elec: "to get the electric consumption data of 'conso_elec' meters"
        dm: "to get the electric consumption data of 'dm' meters"
        conso: "to get the electric consumption data of 'conso' meters"
        10_min: "to get the electric consumption data of '10_min' meters"
        elec_act_cons: "to get the electric consumption data of 'elec_act_cons' meters"
        consumption_elec: "to get the electric consumption data of 'consumption_elec' meters"
        cdc: "to get the electric power data of 'cdc' meters"     
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site" 
helexia_demo:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, electric power, gas, water, etc.).
      mode: in
      indicator_options:
        conso_elec: "to get the electric consumption data of 'conso_elec' meters"
        dm: "to get the electric consumption data of 'dm' meters"
        conso: "to get the electric consumption data of 'conso' meters"
        elec_act_cons: "to get the electric consumption data of 'elec_act_cons' meters"
        consumption_elec: "to get the electric consumption data of 'consumption_elec' meters"
        elec_cons: "to get the electric consumption data of 'elec_cons' meters"                
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site" 
greenbirdie:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric consumption, gas, water, etc.).
      mode: in
      indicator_options:
        conso_mensuelle_enedis: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
        conso_enedis: "to get the electric consumption data of 'conso_enedis' meters"
        conso_annuelle_enedis: "to get the electric consumption data of 'conso_annuelle_enedis' meters"
        energie_annuelle_grdf: "to get the gas consumption data of 'energie_annuelle_grdf' meters"
        energie_facturante_grdf: "to get the gas consumption data of 'energie_facturante_grdf' meters"
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"
spiefacilities:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids and environmental metrics
        (e.g., electric consumption, gas, water, temperature, DJU).
      mode: in
      indicator_options:
        DJF_PRO_26: "to get DJU (degree days) data from 'DJF_PRO_26'"
        DJU_PRO_18_COSTIC: "to get DJU (degree days) data from 'DJU_PRO_18_COSTIC'"
        GENERAL_EAU_CONSO: "to get the water consumption data of 'GENERAL_EAU_CONSO' meters"
        GENERAL_GAZ_CONSO: "to get the gas consumption data of 'GENERAL_GAZ_CONSO' meters"
        TEMPERATURE_AMBIANTE: "to get ambient temperature data from 'TEMPERATURE_AMBIANTE'"
        GENERAL_ELEC_CONSO: "to get the electric consumption data of 'GENERAL_ELEC_CONSO' meters"
    - id: consumption_meter_supply
      description: |
        Compute the consumption based on meter supply.
        The fluid has to be given in input. Depending on it, consumption is returned in:
          - m³ for WATER
          - kWh for all other fluids
          
        For fluids GAS, WOOD, FOL, FOD, COAL, PROPANE, or LPG, normalized consumption is available
        in PCI (lower heating value) and PCS (higher heating value).

        Consumption can be returned with or without linearization (prorata).
      mode: out
      indicator_options:
        consumption_meter_supply: >
          This is a flexible indicator name and must be composed with parts:
          
          • Fluid code (**mandatory**):  
            Must be appended after a dash (-).  
            Example: `consumption_meter_supply-ELEC`
          
          • Fluid option (**optional**):  
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.  
            Example: `consumption_meter_supply_pci-GAS`
          
          • Prorata option (**optional**):  
            Can be `no_prorata`, appended after another dash following the fluid code.  
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`

          Valid fluid codes: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL. 
    - id: consumption_meter_label
      description: |
        This algorithm aims at giving the meter consumption for all the meters with the same label.
        
        Some options can be given in input:
          - Fluid option (e.g., PCI or PCS)
          - Proratisation mode (e.g., no_prorata)

        In output, the consumption is returned in the same unit as CMD for all meters sharing the requested label.
      mode: out
      indicator_options:
        consumption_meter_label: >
          This indicator must be composed in the following structure:
          
          • Label (**mandatory**):  
            Found at the end of the indicator name after removing optional parts.  
            It may include dashes.  
            Example: `pci-no_prorata-my-label-with-dashes`

          • Fluid option (**optional**):  
            If present, appears at the very start (first 3 characters) before the first dash.  
            Accepted values: `pci`, `pcs`  
            Example: `pci-mylabel`, `pcs-anotherlabel`

          • Prorata option (**optional**):  
            If present, appears just before the label, separated by a dash.  
            Example: `pci-no_prorata-mylabel`, `no_prorata-mylabel`

          Final format:  
            `[fluid_option-][prorata_option-]label`

          Examples:  
            - `pci-mylabel`  
            - `pcs-no_prorata-building_energy`  
            - `my_custom_label` 
    - id: commitment
      description: |
        This algorithm aims at computing energy performance indicators defined in a commitment.
        Commitments are linked to a specific site and can be aggregated by type to compute average performance
        if the user requests global results across commitments.

        The performance is defined by the indicator name within the commitment.
      mode: out
      indicator_options:
        COM_1.1: >
          === COM Mode ===
          • Used to compute global/aggregated performance over multiple commitments at a site.

          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N’B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l’usage chauffage.
              - 'contract-N’B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - "formula": "{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N’B}"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum    
    - id: profit_sharing_multisite
      description: |
        This algorithms aims at giving the client and operator profit (intéressement) of a commitment. 
        The profit can be returned for a specific commitment or by site for a commitment.
      mode: out
      formula:
        Intéressement exploitant : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
        Intéressement client : "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"
emera:
  algorithms:
    - id: sum_sensor_labels
      description: |
        Retrieves aggregated sensor data and works for all fluids (e.g., electric power, gas, heat consumption, and submetering).
      mode: in
      indicator_options:
        partfixegaz: "to get fixed gas share data from 'partfixegaz'"
        puissance_enedis: "to get the electric power data of 'puissance_enedis' meters"
        energie_facturante_lin_grdf: "to get the gas consumption data of 'energie_facturante_lin_grdf' meters"
        conso_chaleur_agelia: "to get the heat consumption data of 'conso_chaleur_agelia' meters"
        conso_chaleur: "to get the heat consumption data of 'conso_chaleur' meters"
        emera-cta2024: "to get submetering data from 'emera-cta2024'"
    - id: site
      description: |
        Uses the site service to get the site properties like total area, typology, etc.
      mode: out
      indicator_options:
        site.total_area_m2: "to get total area of the site in square meter"
        site.typology: "to get the typology of the site"        
emission_meter_supply:
  algorithms:
    - id: emission_meter_supply
      description: |
        This algorithm aims at giving the CO2 emission based on the meter supply consumption.
      mode: out
      indicator_options:
        emission_meter_supply: >
          To get CO2 emissions based on supply meter consumption. 
          You should add the fluid at the end, prefixed by a dash.
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, 
          PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL, ALLW.
true_commitment:
  algorithms:
    - id: commitment
      description: |
        This algorithm aims at computing energy performance indicators defined in a commitment.
        Commitments are linked to a specific site and can be aggregated by type to compute average performance
        if the user requests global results across commitments.

        The performance is defined by the indicator name within the commitment.
      mode: in
      indicator_options:
        COM_1.1: >
          The algorithm supports two modes: **COD** and **COM**.

          === COD Mode ===
          • Used to compute the result for a specific commitment only.
          
          **Format:**
            COD-<commitment_id>-<formula_type>-<indicator_name>[-<cumulative_option>]

          **Field breakdown:**
            - COD (Mandatory): fixed keyword 
            - commitment_id (Mandatory): alphanumeric ID of the commitment (must be specified in the request).
            - formula_type (Mandatory): can be `contract`, `real`. and it's `contract` for all indicator_name except for "NC" it's `real`.
            - indicator_name (Mandatory): can one of the following (must be specified in the request):
              - "q" quantité d'énergie nécessaire pour chauffer 1 m3 d’eau chaude sanitaire à la température de consigne
              - "DJU" Degrés Jours, ils représentent le potentiel de chauffage à remplir pour contrer la rigueur climatique dans le contrat
              - "DJR" Degrés Jours réels , ils représentent le potentiel de chauffage à remplir pour contrer la rigueur climatique observés
              - "NB" consommation théorique pour une période de chauffe dans des conditions climatiques théoriques
              - "N'B" cible contractuelle pour la consommation de chauffage.
              - "NC" consommation réelle allouée à l’usage chauffage 
            - cumulative_option (Optional): `cum`, `sum`, `average` .
            - La performance du contrat se calcul comme ((NC/N'B)-1)*100.
          Example:
            COD-AWtqFJ6Jj1tWTm3VWHLd-contract-N'B-cum

          === COM Mode ===
          • Used to compute global/aggregated performance over multiple commitments at a site.

          **Format:**
            COM-<category>#<type>#<market>#<formula_type>-<indicator_name>[-<cumulative_option>]

          **Field breakdown:**
            - COM (Mandatory): fixed keyword 
            - category (Mandatory): `co2`, `energy`. (must be specified in the request)
            - type (Mandatory):`Suivi P1 - chaleur`. 
            - market (Mandatory): market name or empty 
            - formula_type (Mandatory): can be `budget`, `contract`, `real` (must be specified in the request).
            - indicator_name (Mandatory): can one of the following:
              - "q" quantité d'énergie nécessaire pour chauffer 1 m3 d’eau chaude sanitaire à la température de consigne
              - "DJU" Degrés Jours, ils représentent le potentiel de chauffage à remplir pour contrer la rigueur climatique dans le contrat
              - "DJR" Degrés Jours réels , ils représentent le potentiel de chauffage à remplir pour contrer la rigueur climatique observés
              - "NB" consommation théorique pour une période de chauffe dans des conditions climatiques théoriques
              - "N'B" cible contractuelle pour la consommation de chauffage.
              - "NC" consommation réelle allouée à l’usage chauffage 
            - cumulative_option (Optional): `cum`, `sum`, `average`.

          Example:
            COM-co2#Suivi P1 - chaleur#empty#contract-N'B-cum

        COM_1.2: >
          It is possible to include both **COM** and **COD** indicators in the same request 
          to retrieve both global and specific commitment performance metrics simultaneously.